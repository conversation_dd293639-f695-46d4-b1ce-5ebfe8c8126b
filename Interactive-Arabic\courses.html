<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Courses - Interactive Arabic Learning</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="courses.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html" style="text-decoration: none; color: inherit;">
                    <div class="logo-main">IQA</div>
                    <div class="logo-subtitle">Interactive Quranic Arabic</div>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="courses.html" class="nav-link active">Courses</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">About Us</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="courses-container">
        <div class="background-pattern"></div>
        
        <div class="courses-content">
            <div class="page-header">
                <h1 class="page-title">Our Courses</h1>
                <p class="page-subtitle">Choose your path to mastering Quranic Arabic</p>
            </div>

            <div class="loading-state" id="loadingState">
                <div class="loading-spinner"></div>
                <p>Loading courses...</p>
            </div>

            <div class="courses-grid" id="coursesGrid">
                <!-- Course cards will be dynamically inserted here -->
            </div>

            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">📚</div>
                <h3>No courses available yet</h3>
                <p>Check back soon for new courses!</p>
            </div>
        </div>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-square square-1"></div>
        </div>
    </div>

    <script src="courses.js"></script>
</body>
</html>
