/* Courses Page Specific Styles */

.courses-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
    padding-top: 100px; /* Account for fixed navbar */
}

.courses-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-title {
    font-family: '<PERSON><PERSON>', serif;
    font-size: clamp(2.5rem, 4vw, 3.5rem);
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
    text-shadow: 0 2px 4px var(--shadow-light);
}

.page-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: clamp(1rem, 2vw, 1.2rem);
    color: var(--dark-brown);
    margin: 0;
    opacity: 0.9;
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Course Card */
.course-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(212, 165, 116, 0.3);
    box-shadow: 0 10px 30px var(--shadow-light);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px var(--shadow-medium);
    border-color: rgba(212, 165, 116, 0.5);
}

.course-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--light-brown) 0%, var(--primary-brown) 100%);
    position: relative;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

/* Placeholder for missing images */
.course-image.placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--warm-white);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.course-content {
    padding: 1.5rem;
}

.course-title {
    font-family: 'Amiri', serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.course-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.difficulty-badge {
    background: var(--accent-gold);
    color: var(--warm-white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
}

.course-duration {
    color: var(--dark-brown);
    font-size: 0.9rem;
    font-weight: 500;
}

.lessons-count {
    color: var(--text-dark);
    font-size: 0.8rem;
    font-weight: 500;
    opacity: 0.8;
}

.course-description {
    font-family: 'Inter', sans-serif;
    font-size: 0.95rem;
    color: var(--text-dark);
    line-height: 1.5;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.course-button {
    background: linear-gradient(135deg, var(--primary-brown) 0%, var(--accent-gold) 100%);
    color: var(--warm-white);
    border: none;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.course-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.course-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--shadow-medium);
}

.course-button:hover::before {
    left: 100%;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-dark);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--light-brown);
    border-top: 3px solid var(--primary-brown);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-dark);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-family: 'Inter', sans-serif;
    opacity: 0.8;
}

/* Error State */
.error-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: var(--text-dark);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(212, 165, 116, 0.3);
    margin: 2rem 0;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.error-state h3 {
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.error-state p {
    font-family: 'Inter', sans-serif;
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

/* Active nav link */
.nav-link.active {
    color: var(--primary-brown);
    background: rgba(212, 165, 116, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .courses-container {
        padding-top: 80px;
    }
    
    .courses-content {
        padding: 1rem;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .course-meta {
        gap: 0.5rem;
    }
    
    .page-header {
        margin-bottom: 2rem;
    }
}

@media (max-width: 480px) {
    .course-content {
        padding: 1rem;
    }
    
    .course-image {
        height: 160px;
    }
}
