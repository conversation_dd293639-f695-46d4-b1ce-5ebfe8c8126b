* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-brown: #D4A574;
    --light-brown: #E8D5B7;
    --dark-brown: #B8956A;
    --cream: #F5F0E8;
    --warm-white: #FEFCF8;
    --accent-gold: #C9A96E;
    --text-dark: #5D4E37;
    --shadow-light: rgba(212, 165, 116, 0.2);
    --shadow-medium: rgba(212, 165, 116, 0.4);
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--cream) 50%, var(--light-brown) 100%);
    min-height: 100vh;
    overflow: hidden;
    position: relative;
}

.container {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 20%, var(--shadow-light) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, var(--shadow-light) 0%, transparent 50%),
        radial-gradient(circle at 40% 70%, var(--shadow-light) 0%, transparent 30%);
    opacity: 0.6;
}

.content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 900px;
    padding: 2rem;
}

.hero-section {
    position: relative;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 30px;
    border: 1px solid rgba(212, 165, 116, 0.3);
    box-shadow: 
        0 20px 40px var(--shadow-light),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.title {
    font-family: 'Amiri', serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: var(--text-dark);
    line-height: 1.2;
    text-shadow: 0 2px 4px var(--shadow-light);
    letter-spacing: -0.02em;
    margin: 0;
    position: relative;
}

.decorative-element {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid var(--primary-brown);
    border-radius: 50%;
    opacity: 0.6;
}

.decorative-element::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: var(--accent-gold);
    border-radius: 50%;
    opacity: 0.7;
}

.top-left {
    top: -30px;
    left: -30px;
    animation: float 6s ease-in-out infinite;
}

.top-right {
    top: -30px;
    right: -30px;
    animation: float 6s ease-in-out infinite 2s;
}

.bottom-left {
    bottom: -30px;
    left: -30px;
    animation: float 6s ease-in-out infinite 4s;
}

.bottom-right {
    bottom: -30px;
    right: -30px;
    animation: float 6s ease-in-out infinite 1s;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-brown), var(--accent-gold));
    opacity: 0.1;
}

.circle-1 {
    width: 120px;
    height: 120px;
    top: 10%;
    left: 10%;
    animation: drift 20s linear infinite;
}

.circle-2 {
    width: 80px;
    height: 80px;
    top: 70%;
    right: 15%;
    animation: drift 25s linear infinite reverse;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation: drift 30s linear infinite;
}

.floating-square {
    position: absolute;
    background: linear-gradient(45deg, var(--light-brown), var(--primary-brown));
    opacity: 0.08;
    transform: rotate(45deg);
}

.square-1 {
    width: 60px;
    height: 60px;
    top: 30%;
    right: 10%;
    animation: rotate-drift 35s linear infinite;
}

.square-2 {
    width: 40px;
    height: 40px;
    bottom: 40%;
    right: 30%;
    animation: rotate-drift 40s linear infinite reverse;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes drift {
    0% {
        transform: translateX(-50px) translateY(-50px);
    }
    25% {
        transform: translateX(50px) translateY(-100px);
    }
    50% {
        transform: translateX(100px) translateY(50px);
    }
    75% {
        transform: translateX(-30px) translateY(100px);
    }
    100% {
        transform: translateX(-50px) translateY(-50px);
    }
}

@keyframes rotate-drift {
    0% {
        transform: rotate(45deg) translateX(-30px) translateY(-30px);
    }
    25% {
        transform: rotate(135deg) translateX(30px) translateY(-60px);
    }
    50% {
        transform: rotate(225deg) translateX(60px) translateY(30px);
    }
    75% {
        transform: rotate(315deg) translateX(-20px) translateY(60px);
    }
    100% {
        transform: rotate(405deg) translateX(-30px) translateY(-30px);
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 1.5rem;
        margin: 1rem;
    }
    
    .decorative-element {
        width: 40px;
        height: 40px;
    }
    
    .decorative-element::before {
        width: 20px;
        height: 20px;
    }
    
    .floating-circle, .floating-square {
        opacity: 0.05;
    }
}
