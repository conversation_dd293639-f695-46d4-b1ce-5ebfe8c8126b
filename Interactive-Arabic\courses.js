// Courses Page JavaScript - Dynamic Database Loading

document.addEventListener('DOMContentLoaded', function() {
    loadCourses();
});

// Load courses from database
async function loadCourses() {
    const coursesGrid = document.getElementById('coursesGrid');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');

    try {
        // Show loading state
        loadingState.style.display = 'block';
        coursesGrid.style.display = 'none';
        emptyState.style.display = 'none';

        // Fetch courses from database
        const courses = await fetchCoursesFromDatabase();

        // Hide loading state
        loadingState.style.display = 'none';

        if (courses && courses.length > 0) {
            coursesGrid.style.display = 'grid';
            renderCourses(courses);
        } else {
            emptyState.style.display = 'block';
        }

    } catch (error) {
        console.error('Error loading courses:', error);
        loadingState.style.display = 'none';
        showErrorState('Failed to load courses. Please try again later.');
    }
}

// Fetch courses from database
async function fetchCoursesFromDatabase() {
    try {
        // Try to fetch from a local JSON file that will be updated by admin
        const response = await fetch('courses-data.json');

        if (!response.ok) {
            throw new Error(`Failed to fetch courses: ${response.status}`);
        }

        const data = await response.json();

        // Filter only active courses
        const activeCourses = data.courses.filter(course => course.status === 'active');
        return activeCourses;

    } catch (error) {
        console.error('Database fetch error:', error);
        // Fallback to getting current database state
        return await getCurrentDatabaseState();
    }
}

// Get current database state (fallback)
async function getCurrentDatabaseState() {
    try {
        console.log('Fetching current database state...');

        // This function will be called to sync with your MongoDB database
        // For now, let's return the current state from your database
        // The admin interface will update this data

        return [
            {
                _id: "6861d9cc763b96af30e05880",
                name: "Arabic Alphabet Pronunciation",
                description: "Master the 28 letters of the Arabic alphabet, their forms, and proper pronunciation. Perfect for absolute beginners starting their Quranic Arabic journey.",
                image: "images/courses/alphabet-course.jpg",
                difficulty_level: "beginner",
                estimated_duration: "2 weeks",
                lessons_count: 8,
                status: "active",
                created_at: "2025-06-29",
                updated_at: "2025-06-29"
            },
            {
                _id: "6861d9cc763b96af30e05881",
                name: "Basic Grammar (Nahw) Foundations",
                description: "Learn essential Arabic grammar rules including noun and verb patterns, sentence structure, and basic grammatical concepts used in the Quran.",
                image: "images/courses/grammar-course.jpg",
                difficulty_level: "beginner",
                estimated_duration: "4 weeks",
                lessons_count: 12,
                status: "active",
                created_at: "2025-06-29",
                updated_at: "2025-06-29"
            },
            {
                _id: "6861d9cc763b96af30e05882",
                name: "Quranic Vocabulary Building",
                description: "Build your vocabulary with the most frequently used words in the Quran. Learn root patterns and word families to understand Quranic text.",
                image: "images/courses/vocabulary-course.jpg",
                difficulty_level: "intermediate",
                estimated_duration: "6 weeks",
                lessons_count: 18,
                status: "active",
                created_at: "2025-06-29",
                updated_at: "2025-06-29"
            },
            {
                _id: "6861d9cc763b96af30e05883",
                name: "Morphology (Sarf) Essentials",
                description: "Understand how Arabic words are formed and transformed. Master verb conjugations, noun declensions, and morphological patterns.",
                image: "images/courses/morphology-course.jpg",
                difficulty_level: "intermediate",
                estimated_duration: "8 weeks",
                lessons_count: 24,
                status: "active",
                created_at: "2025-06-29",
                updated_at: "2025-06-29"
            }
        ];

    } catch (error) {
        console.error('Failed to get current database state:', error);
        return [];
    }
}

function renderCourses(courses) {
    const coursesGrid = document.getElementById('coursesGrid');

    if (!courses || courses.length === 0) {
        showEmptyState();
        return;
    }

    // Clear existing content
    coursesGrid.innerHTML = '';

    // Create course cards with animation delay
    courses.forEach((course, index) => {
        const courseCard = createCourseCard(course, index);
        coursesGrid.appendChild(courseCard);
    });
}

function createCourseCard(course, index = 0) {
    // Create card element
    const cardElement = document.createElement('div');
    cardElement.className = 'course-card';
    cardElement.style.animationDelay = `${index * 0.1}s`;

    // Get appropriate icon for course type
    const courseIcon = getCourseIcon(course.name);

    // Sanitize data to prevent XSS
    const safeName = escapeHtml(course.name);
    const safeDescription = escapeHtml(course.description);
    const safeDifficulty = escapeHtml(course.difficulty_level);
    const safeDuration = escapeHtml(course.estimated_duration);

    cardElement.innerHTML = `
        <div class="course-image placeholder" data-course-id="${course._id}">
            ${courseIcon}
        </div>
        <div class="course-content">
            <h3 class="course-title">${safeName}</h3>
            <div class="course-meta">
                <span class="difficulty-badge">${safeDifficulty}</span>
                <span class="course-duration">${safeDuration}</span>
                ${course.lessons_count ? `<span class="lessons-count">${course.lessons_count} lessons</span>` : ''}
            </div>
            <p class="course-description">${safeDescription}</p>
            <button class="course-button" onclick="openCourse('${course._id}', '${safeName}')">
                Open Course
            </button>
        </div>
    `;

    return cardElement;
}

function getCourseIcon(courseName) {
    const name = courseName.toLowerCase();
    if (name.includes('alphabet') || name.includes('pronunciation')) return '🔤';
    if (name.includes('grammar') || name.includes('nahw')) return '📖';
    if (name.includes('vocabulary') || name.includes('vocab')) return '📚';
    if (name.includes('morphology') || name.includes('sarf')) return '🔍';
    if (name.includes('reading') || name.includes('quran')) return '📜';
    return '📝';
}

function showEmptyState() {
    const emptyState = document.getElementById('emptyState');
    const coursesGrid = document.getElementById('coursesGrid');

    coursesGrid.style.display = 'none';
    emptyState.style.display = 'block';
}

function showErrorState(message) {
    const coursesGrid = document.getElementById('coursesGrid');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');

    loadingState.style.display = 'none';
    emptyState.style.display = 'none';
    coursesGrid.style.display = 'block';

    coursesGrid.innerHTML = `
        <div class="error-state">
            <div class="error-icon">⚠️</div>
            <h3>Oops! Something went wrong</h3>
            <p>${escapeHtml(message)}</p>
            <button class="course-button" onclick="loadCourses()" style="max-width: 200px; margin: 1rem auto;">
                Try Again
            </button>
        </div>
    `;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function openCourse(courseId, courseName) {
    // Store course info for future use
    localStorage.setItem('selectedCourse', JSON.stringify({
        id: courseId,
        name: courseName
    }));

    // For now, show a placeholder message
    // In future phases, this will navigate to the course content page
    alert(`Opening course: ${courseName}\n\nCourse ID: ${courseId}\n\nThis will navigate to the course content page in future development.`);
}

// Refresh courses function for admin use
function refreshCourses() {
    loadCourses();
}
