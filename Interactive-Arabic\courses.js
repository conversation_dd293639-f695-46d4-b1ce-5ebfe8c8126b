// Courses Page JavaScript
// This will be enhanced in Phase 3 with dynamic database loading

document.addEventListener('DOMContentLoaded', function() {
    // For now, we'll create static course cards to test the layout
    // This will be replaced with dynamic loading in Phase 3
    
    const coursesGrid = document.getElementById('coursesGrid');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    
    // Sample course data (will be replaced with database fetch)
    const sampleCourses = [
        {
            name: "Arabic Alphabet & Pronunciation",
            description: "Master the 28 letters of the Arabic alphabet, their forms, and proper pronunciation. Perfect for absolute beginners starting their Quranic Arabic journey.",
            image: "images/courses/alphabet-course.jpg",
            difficulty_level: "beginner",
            estimated_duration: "2 weeks",
            lessons_count: 8
        },
        {
            name: "Basic Grammar (Nahw) Foundations",
            description: "Learn essential Arabic grammar rules including noun and verb patterns, sentence structure, and basic grammatical concepts used in the Quran.",
            image: "images/courses/grammar-course.jpg",
            difficulty_level: "beginner",
            estimated_duration: "4 weeks",
            lessons_count: 12
        },
        {
            name: "Quranic Vocabulary Building",
            description: "Build your vocabulary with the most frequently used words in the Quran. Learn root patterns and word families to understand Quranic text.",
            image: "images/courses/vocabulary-course.jpg",
            difficulty_level: "intermediate",
            estimated_duration: "6 weeks",
            lessons_count: 18
        },
        {
            name: "Morphology (Sarf) Essentials",
            description: "Understand how Arabic words are formed and transformed. Master verb conjugations, noun declensions, and morphological patterns.",
            image: "images/courses/morphology-course.jpg",
            difficulty_level: "intermediate",
            estimated_duration: "8 weeks",
            lessons_count: 24
        }
    ];
    
    // Simulate loading delay
    setTimeout(() => {
        loadingState.style.display = 'none';
        renderCourses(sampleCourses);
    }, 1000);
});

function renderCourses(courses) {
    const coursesGrid = document.getElementById('coursesGrid');
    const emptyState = document.getElementById('emptyState');
    
    if (courses.length === 0) {
        emptyState.style.display = 'block';
        return;
    }
    
    coursesGrid.innerHTML = courses.map(course => createCourseCard(course)).join('');
}

function createCourseCard(course) {
    // Get appropriate icon for course type
    const courseIcon = getCourseIcon(course.name);
    
    return `
        <div class="course-card">
            <div class="course-image placeholder">
                ${courseIcon}
            </div>
            <div class="course-content">
                <h3 class="course-title">${course.name}</h3>
                <div class="course-meta">
                    <span class="difficulty-badge">${course.difficulty_level}</span>
                    <span class="course-duration">${course.estimated_duration}</span>
                </div>
                <p class="course-description">${course.description}</p>
                <button class="course-button" onclick="openCourse('${course.name}')">
                    Open Course
                </button>
            </div>
        </div>
    `;
}

function getCourseIcon(courseName) {
    if (courseName.includes('Alphabet')) return '🔤';
    if (courseName.includes('Grammar')) return '📖';
    if (courseName.includes('Vocabulary')) return '📚';
    if (courseName.includes('Morphology')) return '🔍';
    return '📝';
}

function openCourse(courseName) {
    // Placeholder function - will be implemented later
    alert(`Opening course: ${courseName}\n\nThis functionality will be implemented in future phases.`);
}
